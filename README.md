# فاحص الاستلال بالذكاء الاصطناعي
# AI-Powered Plagiarism Detection Application

تطبيق احترافي لفحص الاستلال باستخدام تقنيات الذكاء الاصطناعي المتقدمة، يدعم اللغتين العربية والإنجليزية.

## 🌟 المميزات الرئيسية

### 🔍 فحص متقدم للاستلال
- **فحص دلالي**: استخدام نماذج Sentence Transformers لفهم المعنى الحقيقي للنصوص
- **فحص حرفي**: كشف التطابق المباشر والنسخ الحرفي
- **كشف إعادة الصياغة**: تحديد النصوص المعاد صياغتها بذكاء
- **تحليل متعدد المستويات**: فحص على مستوى الجمل والفقرات

### 🧠 تقنيات الذكاء الاصطناعي
- **نماذج لغوية متقدمة**: استخدام BERT وTransformers
- **معالجة اللغات الطبيعية**: دعم spaCy وNLTK
- **تحليل دلالي**: مقارنة المعاني وليس فقط الكلمات
- **تعلم آلي**: تحسين دقة الكشف باستمرار

### 📁 دعم تنسيقات متعددة
- **PDF**: استخراج النص من ملفات PDF المعقدة
- **Word**: دعم ملفات .docx و .doc
- **نصوص**: ملفات .txt بترميزات مختلفة
- **معالجة ذكية**: تنظيف وتحسين النصوص المستخرجة

### 🌐 مصادر مرجعية شاملة
- **ويكيبيديا**: البحث في المقالات العربية والإنجليزية
- **مصادر أكاديمية**: جمع من مواقع البحث العلمي
- **قاعدة بيانات محلية**: تخزين وإدارة المصادر المرجعية
- **تحديث تلقائي**: إضافة مصادر جديدة باستمرار

### 📊 تقارير مفصلة
- **تقارير PDF**: تقارير احترافية بالعربية والإنجليزية
- **رسوم بيانية**: تمثيل مرئي للنتائج
- **تحليل تفصيلي**: عرض كل جملة مشبوهة ومصدرها
- **توصيات**: اقتراحات لتحسين النص

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
- Python 3.8 أو أحدث
- Windows 10/11
- 4 GB RAM على الأقل
- 2 GB مساحة فارغة

### التثبيت من المصدر

1. **استنساخ المشروع**:
```bash
git clone https://github.com/your-repo/plagiarism-detector.git
cd plagiarism-detector
```

2. **تثبيت المتطلبات**:
```bash
pip install -r requirements.txt
```

3. **تحميل النماذج المطلوبة**:
```bash
python -m spacy download en_core_web_sm
```

4. **تشغيل التطبيق**:
```bash
python main.py
```

### بناء ملف تنفيذي

لإنشاء ملف .exe مستقل:

```bash
python build_exe.py
```

سيتم إنشاء الملف التنفيذي في مجلد `dist/`.

## 🎯 طريقة الاستخدام

### 1. تشغيل التطبيق
- شغل `main.py` أو الملف التنفيذي
- انتظر تحميل نماذج الذكاء الاصطناعي

### 2. اختيار الملف
- اضغط "تصفح" لاختيار الملف
- الأنواع المدعومة: PDF, Word, TXT
- تأكد من أن الملف يحتوي على نص كافٍ (أكثر من 50 حرف)

### 3. بدء الفحص
- اضغط "فحص الاستلال"
- انتظر انتهاء التحليل (قد يستغرق دقائق حسب حجم الملف)

### 4. مراجعة النتائج
- **ملخص النتائج**: نسبة الاستلال الإجمالية ومستوى المخاطر
- **الجمل المشبوهة**: قائمة تفصيلية بالجمل المشكوك فيها
- **المصادر المطابقة**: المراجع التي تم العثور على تطابق معها

### 5. حفظ التقرير
- اضغط "حفظ التقرير (PDF)"
- اختر مكان الحفظ
- سيتم إنتاج تقرير شامل بالنتائج

## 🏗️ بنية المشروع

```
plagiarism-detector/
├── src/
│   ├── core/
│   │   ├── plagiarism_detector.py    # محرك فحص الاستلال
│   │   └── source_manager.py         # مدير المصادر المرجعية
│   ├── gui/
│   │   └── main_window.py            # الواجهة الرسومية
│   └── utils/
│       ├── file_processor.py         # معالج الملفات
│       └── report_generator.py       # مولد التقارير
├── data/
│   └── sources/                      # قاعدة بيانات المصادر
├── models/                           # نماذج الذكاء الاصطناعي
├── reports/                          # التقارير المُنتجة
├── main.py                           # الملف الرئيسي
├── test_app.py                       # اختبارات التطبيق
├── build_exe.py                      # سكريبت بناء EXE
└── requirements.txt                  # المتطلبات
```

## 🔧 التكوين المتقدم

### إعدادات الكشف

يمكن تعديل حساسية الكشف في `src/core/plagiarism_detector.py`:

```python
self.similarity_threshold = 0.8      # عتبة التشابه الدلالي
self.exact_match_threshold = 0.95    # عتبة التطابق الحرفي
```

### إضافة مصادر مخصصة

```python
from src.core.source_manager import SourceManager

manager = SourceManager()
manager.add_source(
    title="مصدر مخصص",
    content="محتوى المصدر...",
    url="https://example.com",
    source_type="custom",
    language="ar"
)
```

## 🧪 الاختبار

تشغيل الاختبارات الشاملة:

```bash
python test_app.py
```

## 📈 الأداء

- **سرعة المعالجة**: ~0.1 ثانية لكل جملة
- **دقة الكشف**: >90% للاستلال الحرفي، >85% للاستلال الدلالي
- **استهلاك الذاكرة**: 1-2 GB أثناء التشغيل
- **حجم التطبيق**: ~500 MB (يشمل جميع النماذج)

## 🛠️ التقنيات المستخدمة

### الذكاء الاصطناعي
- **Sentence Transformers**: للتحليل الدلالي
- **spaCy**: لمعالجة اللغات الطبيعية
- **NLTK**: لتقسيم النصوص
- **scikit-learn**: للحوسبة العلمية

### معالجة الملفات
- **PyPDF2 & pdfplumber**: لملفات PDF
- **python-docx**: لملفات Word
- **Beautiful Soup**: لاستخراج النصوص من الويب

### الواجهة والتقارير
- **Tkinter**: للواجهة الرسومية
- **ReportLab**: لإنتاج تقارير PDF
- **Matplotlib**: للرسوم البيانية

## 🔒 الأمان والخصوصية

- **معالجة محلية**: جميع النصوص تُعالج محلياً
- **لا تخزين سحابي**: البيانات لا تُرسل لخوادم خارجية
- **تشفير قاعدة البيانات**: حماية المصادر المرجعية
- **حذف الملفات المؤقتة**: تنظيف تلقائي بعد المعالجة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم الفني

للدعم الفني والاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق والأمثلة

## 🔄 التحديثات المستقبلية

- [ ] دعم المزيد من اللغات
- [ ] تحسين دقة الكشف
- [ ] واجهة ويب
- [ ] API للمطورين
- [ ] تكامل مع أنظمة إدارة التعلم

---

**تم تطوير هذا التطبيق بواسطة الذكاء الاصطناعي لخدمة المجتمع الأكاديمي والبحثي**
