"""
محرك فحص الاستلال بالذكاء الاصطناعي
Plagiarism Detection Engine with AI
"""

import re
import numpy as np
from typing import List, Dict, Tuple, Optional
from sentence_transformers import SentenceTransformer
import spacy
from sklearn.metrics.pairwise import cosine_similarity
import nltk
from nltk.tokenize import sent_tokenize
import logging

# تحميل النماذج المطلوبة
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    pass

class PlagiarismDetector:
    """
    فئة فحص الاستلال الرئيسية
    Main plagiarism detection class
    """
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """
        تهيئة محرك فحص الاستلال
        Initialize plagiarism detection engine
        """
        self.logger = logging.getLogger(__name__)
        
        # تحميل نموذج sentence-transformers
        try:
            self.sentence_model = SentenceTransformer(model_name)
            self.logger.info(f"تم تحميل النموذج: {model_name}")
        except Exception as e:
            self.logger.error(f"خطأ في تحميل النموذج: {e}")
            raise
        
        # تحميل نموذج spaCy للغة الإنجليزية
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            self.logger.warning("نموذج spaCy غير متوفر، سيتم استخدام المعالجة الأساسية")
            self.nlp = None
        
        # عتبات الكشف
        self.similarity_threshold = 0.8  # عتبة التشابه الدلالي
        self.exact_match_threshold = 0.95  # عتبة التطابق الحرفي
        
    def preprocess_text(self, text: str) -> str:
        """
        معالجة النص الأولية
        Text preprocessing
        """
        # إزالة الأسطر الفارغة والمسافات الزائدة
        text = re.sub(r'\s+', ' ', text.strip())
        
        # إزالة الأرقام والرموز غير المرغوب فيها
        text = re.sub(r'[^\w\s\u0600-\u06FF.,!?;:]', ' ', text)
        
        return text
    
    def extract_sentences(self, text: str) -> List[str]:
        """
        استخراج الجمل من النص
        Extract sentences from text
        """
        # معالجة النص أولاً
        text = self.preprocess_text(text)
        
        # تقسيم النص إلى جمل
        if self.nlp:
            doc = self.nlp(text)
            sentences = [sent.text.strip() for sent in doc.sents if len(sent.text.strip()) > 10]
        else:
            # استخدام NLTK كبديل
            sentences = sent_tokenize(text)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        return sentences
    
    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """
        حساب التشابه الدلالي بين نصين
        Calculate semantic similarity between two texts
        """
        try:
            # تحويل النصوص إلى vectors
            embeddings = self.sentence_model.encode([text1, text2])
            
            # حساب cosine similarity
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            
            return float(similarity)
        except Exception as e:
            self.logger.error(f"خطأ في حساب التشابه الدلالي: {e}")
            return 0.0
    
    def calculate_exact_similarity(self, text1: str, text2: str) -> float:
        """
        حساب التشابه الحرفي بين نصين
        Calculate exact similarity between two texts
        """
        # تحويل النصوص إلى أحرف صغيرة
        text1_lower = text1.lower().strip()
        text2_lower = text2.lower().strip()
        
        # حساب نسبة التطابق الحرفي
        if len(text1_lower) == 0 or len(text2_lower) == 0:
            return 0.0
        
        # استخدام Levenshtein distance
        from difflib import SequenceMatcher
        matcher = SequenceMatcher(None, text1_lower, text2_lower)
        similarity = matcher.ratio()
        
        return similarity
    
    def detect_plagiarism_type(self, similarity_score: float, exact_score: float) -> str:
        """
        تحديد نوع الاستلال
        Determine plagiarism type
        """
        if exact_score >= self.exact_match_threshold:
            return "استلال حرفي"  # Exact plagiarism
        elif similarity_score >= self.similarity_threshold:
            if exact_score > 0.5:
                return "إعادة صياغة"  # Paraphrasing
            else:
                return "استلال معنوي"  # Semantic plagiarism
        else:
            return "لا يوجد استلال"  # No plagiarism
    
    def analyze_text_against_sources(self, input_text: str, source_texts: List[Dict]) -> Dict:
        """
        تحليل النص مقابل المصادر المرجعية
        Analyze text against reference sources
        """
        results = {
            'total_plagiarism_percentage': 0.0,
            'suspicious_sentences': [],
            'detailed_analysis': [],
            'sources_matched': set()
        }
        
        # استخراج الجمل من النص المدخل
        input_sentences = self.extract_sentences(input_text)
        total_sentences = len(input_sentences)
        
        if total_sentences == 0:
            return results
        
        plagiarized_sentences = 0
        
        for i, sentence in enumerate(input_sentences):
            sentence_results = {
                'sentence': sentence,
                'sentence_index': i,
                'matches': [],
                'max_similarity': 0.0,
                'plagiarism_type': 'لا يوجد استلال'
            }
            
            # مقارنة الجملة مع جميع المصادر
            for source in source_texts:
                source_sentences = self.extract_sentences(source.get('content', ''))
                
                for source_sentence in source_sentences:
                    # حساب التشابه الدلالي والحرفي
                    semantic_sim = self.calculate_semantic_similarity(sentence, source_sentence)
                    exact_sim = self.calculate_exact_similarity(sentence, source_sentence)
                    
                    # تحديد نوع الاستلال
                    plagiarism_type = self.detect_plagiarism_type(semantic_sim, exact_sim)
                    
                    if semantic_sim >= self.similarity_threshold or exact_sim >= self.exact_match_threshold:
                        match_info = {
                            'source_title': source.get('title', 'مصدر غير معروف'),
                            'source_url': source.get('url', ''),
                            'matched_sentence': source_sentence,
                            'semantic_similarity': semantic_sim,
                            'exact_similarity': exact_sim,
                            'plagiarism_type': plagiarism_type
                        }
                        
                        sentence_results['matches'].append(match_info)
                        
                        # تحديث أعلى نسبة تشابه
                        if semantic_sim > sentence_results['max_similarity']:
                            sentence_results['max_similarity'] = semantic_sim
                            sentence_results['plagiarism_type'] = plagiarism_type
                        
                        # إضافة المصدر إلى قائمة المصادر المطابقة
                        results['sources_matched'].add(source.get('title', 'مصدر غير معروف'))
            
            # إضافة الجملة إلى النتائج إذا كانت مشبوهة
            if sentence_results['matches']:
                results['suspicious_sentences'].append(sentence_results)
                plagiarized_sentences += 1
            
            results['detailed_analysis'].append(sentence_results)
        
        # حساب النسبة المئوية الإجمالية للاستلال
        results['total_plagiarism_percentage'] = (plagiarized_sentences / total_sentences) * 100
        results['sources_matched'] = list(results['sources_matched'])
        
        return results
    
    def generate_summary_report(self, analysis_results: Dict) -> Dict:
        """
        إنتاج تقرير ملخص للنتائج
        Generate summary report of results
        """
        summary = {
            'overall_plagiarism_percentage': analysis_results['total_plagiarism_percentage'],
            'total_suspicious_sentences': len(analysis_results['suspicious_sentences']),
            'sources_count': len(analysis_results['sources_matched']),
            'plagiarism_types': {},
            'risk_level': 'منخفض'
        }
        
        # تحليل أنواع الاستلال
        for sentence_data in analysis_results['suspicious_sentences']:
            plag_type = sentence_data['plagiarism_type']
            if plag_type in summary['plagiarism_types']:
                summary['plagiarism_types'][plag_type] += 1
            else:
                summary['plagiarism_types'][plag_type] = 1
        
        # تحديد مستوى المخاطر
        percentage = summary['overall_plagiarism_percentage']
        if percentage >= 30:
            summary['risk_level'] = 'عالي'
        elif percentage >= 15:
            summary['risk_level'] = 'متوسط'
        else:
            summary['risk_level'] = 'منخفض'
        
        return summary
