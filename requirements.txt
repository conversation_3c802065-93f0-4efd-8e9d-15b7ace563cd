# متطلبات تطبيق فحص الاستلال بالذكاء الاصطناعي
# AI and NLP Libraries
sentence-transformers==2.2.2
transformers==4.35.2
torch==2.1.1
spacy==3.7.2
nltk==3.8.1

# Document Processing
PyPDF2==3.0.1
python-docx==1.1.0
pdfplumber==0.10.0

# GUI Framework
tkinter-tooltip==2.1.0
Pillow==10.1.0

# Web Scraping and APIs
requests==2.31.0
beautifulsoup4==4.12.2
wikipedia==1.4.0

# Data Processing
pandas==2.1.3
numpy==1.25.2
scikit-learn==1.3.2

# Database
# sqlite3 (built-in)

# PDF Generation for Reports
reportlab==4.0.7
matplotlib==3.8.2

# Progress Bar
tqdm==4.66.1

# Packaging
pyinstaller==6.2.0
auto-py-to-exe==2.43.1

# Arabic Language Support
arabic-reshaper==3.0.0
python-bidi==0.4.2

# Additional utilities
python-dateutil==2.8.2
regex==2023.10.3
