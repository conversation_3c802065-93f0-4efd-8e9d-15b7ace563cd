"""
تطبيق فحص الاستلال بالذكاء الاصطناعي
AI-Powered Plagiarism Detection Application

الملف الرئيسي لتشغيل التطبيق
Main file to run the application
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع للاستيراد
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.gui.main_window import PlagiarismDetectorGUI
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من تثبيت جميع المتطلبات باستخدام: pip install -r requirements.txt")
    sys.exit(1)

def setup_logging():
    """
    إعداد نظام السجلات
    Setup logging system
    """
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'app.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """
    فحص المتطلبات الأساسية
    Check essential dependencies
    """
    required_packages = [
        'sentence_transformers',
        'transformers',
        'torch',
        'spacy',
        'nltk',
        'PyPDF2',
        'python_docx',
        'requests',
        'beautifulsoup4',
        'wikipedia',
        'pandas',
        'numpy',
        'scikit_learn',
        'reportlab',
        'matplotlib',
        'tqdm',
        'arabic_reshaper',
        'python_bidi'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("المتطلبات التالية غير مثبتة:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nلتثبيت المتطلبات، استخدم الأمر:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def download_required_models():
    """
    تحميل النماذج المطلوبة
    Download required models
    """
    try:
        import nltk
        print("جاري تحميل نماذج NLTK...")
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        
        # محاولة تحميل نموذج spaCy
        try:
            import spacy
            spacy.load("en_core_web_sm")
        except OSError:
            print("تحذير: نموذج spaCy غير متوفر. سيتم استخدام المعالجة الأساسية.")
            print("لتثبيت نموذج spaCy، استخدم الأمر:")
            print("python -m spacy download en_core_web_sm")
        
        print("تم تحميل النماذج بنجاح.")
        return True
        
    except Exception as e:
        print(f"خطأ في تحميل النماذج: {e}")
        return False

def create_directories():
    """
    إنشاء المجلدات المطلوبة
    Create required directories
    """
    directories = [
        "data/sources",
        "models",
        "reports",
        "logs",
        "temp"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)

def main():
    """
    الدالة الرئيسية
    Main function
    """
    print("=" * 60)
    print("فاحص الاستلال بالذكاء الاصطناعي")
    print("AI-Powered Plagiarism Detection Application")
    print("=" * 60)
    
    # إعداد السجلات
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # إنشاء المجلدات المطلوبة
        create_directories()
        logger.info("تم إنشاء المجلدات المطلوبة")
        
        # فحص المتطلبات
        print("جاري فحص المتطلبات...")
        if not check_dependencies():
            logger.error("فشل في فحص المتطلبات")
            input("اضغط Enter للخروج...")
            return
        
        logger.info("تم فحص المتطلبات بنجاح")
        
        # تحميل النماذج المطلوبة
        print("جاري تحميل النماذج المطلوبة...")
        if not download_required_models():
            logger.warning("تحذير: فشل في تحميل بعض النماذج")
        
        # تشغيل التطبيق
        print("جاري تشغيل التطبيق...")
        logger.info("بدء تشغيل التطبيق")
        
        app = PlagiarismDetectorGUI()
        app.run()
        
        logger.info("تم إغلاق التطبيق")
        
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم")
        logger.info("تم إيقاف التطبيق بواسطة المستخدم")
        
    except Exception as e:
        error_msg = f"خطأ غير متوقع: {str(e)}"
        print(error_msg)
        logger.error(error_msg, exc_info=True)
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
