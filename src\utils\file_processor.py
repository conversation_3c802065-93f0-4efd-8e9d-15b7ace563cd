"""
معالج الملفات - قراءة واستخراج النصوص من ملفات مختلفة
File Processor - Reading and extracting text from various file formats
"""

import os
import logging
from typing import Optional, Dict
import PyPDF2
import pdfplumber
from docx import Document
import re

class FileProcessor:
    """
    فئة معالجة الملفات
    File processing class
    """
    
    def __init__(self):
        """
        تهيئة معالج الملفات
        Initialize file processor
        """
        self.logger = logging.getLogger(__name__)
        self.supported_formats = ['.pdf', '.docx', '.doc', '.txt']
    
    def is_supported_format(self, file_path: str) -> bool:
        """
        فحص ما إذا كان تنسيق الملف مدعوم
        Check if file format is supported
        """
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.supported_formats
    
    def extract_text_from_pdf(self, file_path: str) -> Optional[str]:
        """
        استخراج النص من ملف PDF
        Extract text from PDF file
        """
        try:
            text = ""
            
            # محاولة استخدام pdfplumber أولاً (أفضل للنصوص المعقدة)
            try:
                with pdfplumber.open(file_path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                
                if text.strip():
                    self.logger.info(f"تم استخراج النص من PDF باستخدام pdfplumber: {file_path}")
                    return text.strip()
            except Exception as e:
                self.logger.warning(f"فشل pdfplumber، جاري المحاولة مع PyPDF2: {e}")
            
            # استخدام PyPDF2 كبديل
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                
                if text.strip():
                    self.logger.info(f"تم استخراج النص من PDF باستخدام PyPDF2: {file_path}")
                    return text.strip()
                else:
                    self.logger.warning(f"لم يتم العثور على نص في ملف PDF: {file_path}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"خطأ في استخراج النص من PDF {file_path}: {e}")
            return None
    
    def extract_text_from_docx(self, file_path: str) -> Optional[str]:
        """
        استخراج النص من ملف Word (docx)
        Extract text from Word (docx) file
        """
        try:
            doc = Document(file_path)
            text = ""
            
            # استخراج النص من الفقرات
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"
            
            # استخراج النص من الجداول
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text += cell.text + " "
                    text += "\n"
            
            if text.strip():
                self.logger.info(f"تم استخراج النص من Word: {file_path}")
                return text.strip()
            else:
                self.logger.warning(f"لم يتم العثور على نص في ملف Word: {file_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في استخراج النص من Word {file_path}: {e}")
            return None
    
    def extract_text_from_txt(self, file_path: str) -> Optional[str]:
        """
        قراءة النص من ملف نصي
        Read text from text file
        """
        try:
            # محاولة قراءة الملف بترميزات مختلفة
            encodings = ['utf-8', 'utf-16', 'cp1256', 'iso-8859-1', 'windows-1252']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                        if text.strip():
                            self.logger.info(f"تم قراءة الملف النصي بترميز {encoding}: {file_path}")
                            return text.strip()
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    self.logger.warning(f"خطأ في قراءة الملف بترميز {encoding}: {e}")
                    continue
            
            self.logger.error(f"فشل في قراءة الملف النصي بجميع الترميزات: {file_path}")
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في قراءة الملف النصي {file_path}: {e}")
            return None
    
    def clean_extracted_text(self, text: str) -> str:
        """
        تنظيف النص المستخرج
        Clean extracted text
        """
        if not text:
            return ""
        
        # إزالة الأسطر الفارغة المتعددة
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # إزالة المسافات الزائدة
        text = re.sub(r'[ \t]+', ' ', text)
        
        # إزالة الأحرف الخاصة غير المرغوب فيها
        text = re.sub(r'[^\w\s\u0600-\u06FF.,!?;:()\[\]{}"\'-]', ' ', text)
        
        # إزالة المسافات في بداية ونهاية الأسطر
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(line for line in lines if line)
        
        return text.strip()
    
    def extract_text(self, file_path: str) -> Dict:
        """
        استخراج النص من الملف حسب نوعه
        Extract text from file based on its type
        """
        result = {
            'success': False,
            'text': '',
            'file_path': file_path,
            'file_size': 0,
            'error_message': ''
        }
        
        try:
            # فحص وجود الملف
            if not os.path.exists(file_path):
                result['error_message'] = 'الملف غير موجود'
                return result
            
            # فحص حجم الملف
            file_size = os.path.getsize(file_path)
            result['file_size'] = file_size
            
            # فحص حد أقصى لحجم الملف (50 MB)
            max_size = 50 * 1024 * 1024  # 50 MB
            if file_size > max_size:
                result['error_message'] = f'حجم الملف كبير جداً (أكثر من {max_size // (1024*1024)} MB)'
                return result
            
            # فحص تنسيق الملف
            if not self.is_supported_format(file_path):
                result['error_message'] = 'تنسيق الملف غير مدعوم'
                return result
            
            # استخراج النص حسب نوع الملف
            _, ext = os.path.splitext(file_path.lower())
            
            if ext == '.pdf':
                text = self.extract_text_from_pdf(file_path)
            elif ext in ['.docx', '.doc']:
                text = self.extract_text_from_docx(file_path)
            elif ext == '.txt':
                text = self.extract_text_from_txt(file_path)
            else:
                result['error_message'] = 'تنسيق الملف غير مدعوم'
                return result
            
            if text:
                # تنظيف النص
                cleaned_text = self.clean_extracted_text(text)
                
                if len(cleaned_text.strip()) < 50:
                    result['error_message'] = 'النص قصير جداً للفحص (أقل من 50 حرف)'
                    return result
                
                result['success'] = True
                result['text'] = cleaned_text
                self.logger.info(f"تم استخراج النص بنجاح من: {file_path}")
            else:
                result['error_message'] = 'فشل في استخراج النص من الملف'
            
        except Exception as e:
            result['error_message'] = f'خطأ غير متوقع: {str(e)}'
            self.logger.error(f"خطأ في معالجة الملف {file_path}: {e}")
        
        return result
    
    def get_file_info(self, file_path: str) -> Dict:
        """
        الحصول على معلومات الملف
        Get file information
        """
        info = {
            'name': '',
            'size': 0,
            'extension': '',
            'is_supported': False
        }
        
        try:
            if os.path.exists(file_path):
                info['name'] = os.path.basename(file_path)
                info['size'] = os.path.getsize(file_path)
                _, ext = os.path.splitext(file_path.lower())
                info['extension'] = ext
                info['is_supported'] = self.is_supported_format(file_path)
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الملف {file_path}: {e}")
        
        return info
