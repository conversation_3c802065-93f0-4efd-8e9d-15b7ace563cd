"""
سكريبت بناء التطبيق كملف تنفيذي
Build script for creating executable file
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_directories():
    """
    تنظيف مجلدات البناء السابقة
    Clean previous build directories
    """
    directories_to_clean = ['build', 'dist', '__pycache__']
    
    for directory in directories_to_clean:
        if os.path.exists(directory):
            shutil.rmtree(directory)
            print(f"تم حذف مجلد: {directory}")

def create_spec_file():
    """
    إنشاء ملف spec لـ PyInstaller
    Create spec file for PyInstaller
    """
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('data', 'data'),
        ('models', 'models'),
        ('reports', 'reports'),
    ],
    hiddenimports=[
        'sentence_transformers',
        'transformers',
        'torch',
        'spacy',
        'nltk',
        'PyPDF2',
        'docx',
        'pdfplumber',
        'requests',
        'beautifulsoup4',
        'wikipedia',
        'pandas',
        'numpy',
        'sklearn',
        'reportlab',
        'matplotlib',
        'arabic_reshaper',
        'bidi',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'PIL',
        'tqdm',
        'sqlite3',
        'logging',
        'threading',
        'datetime',
        'hashlib',
        'json',
        'urllib',
        'difflib',
        'collections',
        're',
        'io',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PlagiarismDetector',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open('plagiarism_detector.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("تم إنشاء ملف plagiarism_detector.spec")

def install_requirements():
    """
    تثبيت المتطلبات
    Install requirements
    """
    print("جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"خطأ في تثبيت المتطلبات: {e}")
        return False

def build_executable():
    """
    بناء الملف التنفيذي
    Build executable file
    """
    print("جاري بناء الملف التنفيذي...")
    try:
        # استخدام ملف spec
        subprocess.check_call([
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'plagiarism_detector.spec'
        ])
        
        print("تم بناء الملف التنفيذي بنجاح!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"خطأ في بناء الملف التنفيذي: {e}")
        return False
    except FileNotFoundError:
        print("PyInstaller غير مثبت. جاري التثبيت...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            return build_executable()
        except subprocess.CalledProcessError:
            print("فشل في تثبيت PyInstaller")
            return False

def create_assets_directory():
    """
    إنشاء مجلد الأصول
    Create assets directory
    """
    assets_dir = Path("assets")
    assets_dir.mkdir(exist_ok=True)
    
    # إنشاء أيقونة بسيطة إذا لم تكن موجودة
    icon_path = assets_dir / "icon.ico"
    if not icon_path.exists():
        print("تحذير: لم يتم العثور على أيقونة التطبيق")

def copy_additional_files():
    """
    نسخ الملفات الإضافية
    Copy additional files
    """
    if os.path.exists('dist/PlagiarismDetector.exe'):
        dist_dir = Path('dist')
        
        # نسخ ملف README
        readme_content = """
فاحص الاستلال بالذكاء الاصطناعي
AI-Powered Plagiarism Detection Application

تعليمات الاستخدام:
1. شغل ملف PlagiarismDetector.exe
2. اختر الملف المراد فحصه (PDF, Word, أو نص)
3. اضغط على "فحص الاستلال"
4. انتظر انتهاء التحليل
5. احفظ التقرير إذا رغبت

المتطلبات:
- نظام التشغيل: Windows 10/11
- ذاكرة: 4 GB RAM على الأقل
- مساحة: 2 GB مساحة فارغة

ملاحظات:
- التشغيل الأول قد يستغرق وقتاً أطول لتحميل النماذج
- يحتاج اتصال بالإنترنت لجمع المصادر المرجعية
- يدعم اللغتين العربية والإنجليزية

للدعم الفني: تواصل مع المطور
"""
        
        with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("تم إنشاء ملف README.txt")

def main():
    """
    الدالة الرئيسية
    Main function
    """
    print("=" * 60)
    print("بناء تطبيق فاحص الاستلال بالذكاء الاصطناعي")
    print("Building AI Plagiarism Detection Application")
    print("=" * 60)
    
    # تنظيف المجلدات السابقة
    clean_build_directories()
    
    # إنشاء مجلد الأصول
    create_assets_directory()
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("فشل في تثبيت المتطلبات. توقف البناء.")
        return
    
    # إنشاء ملف spec
    create_spec_file()
    
    # بناء الملف التنفيذي
    if build_executable():
        print("\n" + "=" * 60)
        print("تم بناء التطبيق بنجاح!")
        print("الملف التنفيذي موجود في: dist/PlagiarismDetector.exe")
        
        # نسخ الملفات الإضافية
        copy_additional_files()
        
        print("\nيمكنك الآن توزيع مجلد dist كاملاً")
        print("=" * 60)
    else:
        print("فشل في بناء التطبيق")

if __name__ == "__main__":
    main()
