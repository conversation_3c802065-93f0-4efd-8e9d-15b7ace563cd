"""
الواجهة الرسومية الرئيسية لتطبيق فحص الاستلال
Main GUI for Plagiarism Detection Application
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from typing import Optional, Dict
import logging

# إضافة مسار المشروع للاستيراد
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.core.plagiarism_detector import PlagiarismDetector
from src.core.source_manager import SourceManager
from src.utils.file_processor import FileProcessor
from src.utils.report_generator import ReportGenerator

class PlagiarismDetectorGUI:
    """
    فئة الواجهة الرسومية الرئيسية
    Main GUI class
    """
    
    def __init__(self):
        """
        تهيئة الواجهة الرسومية
        Initialize GUI
        """
        self.root = tk.Tk()
        self.setup_logging()
        self.setup_window()
        self.create_widgets()
        
        # تهيئة المكونات الأساسية
        self.detector = None
        self.source_manager = None
        self.file_processor = FileProcessor()
        self.report_generator = ReportGenerator()
        
        # متغيرات الحالة
        self.current_file_path = ""
        self.analysis_results = None
        self.is_analyzing = False
        
        # تهيئة المكونات في خيط منفصل
        self.init_components()
    
    def setup_logging(self):
        """
        إعداد نظام السجلات
        Setup logging system
        """
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('plagiarism_detector.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_window(self):
        """
        إعداد النافذة الرئيسية
        Setup main window
        """
        self.root.title("فاحص الاستلال بالذكاء الاصطناعي - AI Plagiarism Detector")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # تعيين أيقونة التطبيق (إذا كانت متوفرة)
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
        
        # تحسين مظهر التطبيق
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان مخصصة
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#2c3e50')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), foreground='#34495e')
        style.configure('Success.TLabel', foreground='#27ae60')
        style.configure('Warning.TLabel', foreground='#f39c12')
        style.configure('Error.TLabel', foreground='#e74c3c')
    
    def create_widgets(self):
        """
        إنشاء عناصر الواجهة
        Create GUI widgets
        """
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # العنوان الرئيسي
        title_label = ttk.Label(main_frame, text="فاحص الاستلال بالذكاء الاصطناعي", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # قسم اختيار الملف
        self.create_file_selection_section(main_frame)
        
        # قسم النتائج
        self.create_results_section(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_file_selection_section(self, parent):
        """
        إنشاء قسم اختيار الملف
        Create file selection section
        """
        # إطار اختيار الملف
        file_frame = ttk.LabelFrame(parent, text="اختيار الملف للفحص", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # مسار الملف
        ttk.Label(file_frame, text="الملف:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.file_path_var = tk.StringVar()
        self.file_path_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state='readonly')
        self.file_path_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # أزرار التحكم
        self.browse_button = ttk.Button(file_frame, text="تصفح", command=self.browse_file)
        self.browse_button.grid(row=0, column=2, padx=(0, 10))
        
        self.analyze_button = ttk.Button(file_frame, text="فحص الاستلال", command=self.start_analysis, state='disabled')
        self.analyze_button.grid(row=0, column=3)
        
        # معلومات الملف
        self.file_info_label = ttk.Label(file_frame, text="", foreground='#7f8c8d')
        self.file_info_label.grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(10, 0))
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(file_frame, variable=self.progress_var, mode='determinate')
        self.progress_bar.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))
        self.progress_bar.grid_remove()  # إخفاء في البداية
    
    def create_results_section(self, parent):
        """
        إنشاء قسم النتائج
        Create results section
        """
        # دفتر الملاحظات للنتائج
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # تبويب الملخص
        self.create_summary_tab()
        
        # تبويب التفاصيل
        self.create_details_tab()
        
        # تبويب المصادر
        self.create_sources_tab()
    
    def create_summary_tab(self):
        """
        إنشاء تبويب الملخص
        Create summary tab
        """
        summary_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(summary_frame, text="ملخص النتائج")
        
        # نسبة الاستلال الإجمالية
        self.plagiarism_percentage_var = tk.StringVar(value="0%")
        percentage_label = ttk.Label(summary_frame, text="نسبة الاستلال:", style='Header.TLabel')
        percentage_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        self.percentage_value_label = ttk.Label(summary_frame, textvariable=self.plagiarism_percentage_var, 
                                               font=('Arial', 24, 'bold'), foreground='#e74c3c')
        self.percentage_value_label.grid(row=0, column=1, sticky=tk.W, padx=(20, 0), pady=(0, 10))
        
        # مستوى المخاطر
        self.risk_level_var = tk.StringVar(value="غير محدد")
        risk_label = ttk.Label(summary_frame, text="مستوى المخاطر:", style='Header.TLabel')
        risk_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        
        self.risk_value_label = ttk.Label(summary_frame, textvariable=self.risk_level_var, 
                                         font=('Arial', 14, 'bold'))
        self.risk_value_label.grid(row=1, column=1, sticky=tk.W, padx=(20, 0), pady=(0, 10))
        
        # إحصائيات إضافية
        stats_frame = ttk.LabelFrame(summary_frame, text="إحصائيات التحليل", padding="10")
        stats_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        stats_frame.columnconfigure(1, weight=1)
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=8, width=60, state='disabled')
        self.stats_text.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # زر حفظ التقرير
        self.save_report_button = ttk.Button(summary_frame, text="حفظ التقرير (PDF)", 
                                           command=self.save_report, state='disabled')
        self.save_report_button.grid(row=3, column=0, columnspan=2, pady=(20, 0))
    
    def create_details_tab(self):
        """
        إنشاء تبويب التفاصيل
        Create details tab
        """
        details_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(details_frame, text="الجمل المشبوهة")
        
        # قائمة الجمل المشبوهة
        self.details_tree = ttk.Treeview(details_frame, columns=('sentence', 'similarity', 'type', 'source'), 
                                        show='headings', height=15)
        
        # تحديد عناوين الأعمدة
        self.details_tree.heading('sentence', text='الجملة المشبوهة')
        self.details_tree.heading('similarity', text='نسبة التشابه')
        self.details_tree.heading('type', text='نوع الاستلال')
        self.details_tree.heading('source', text='المصدر')
        
        # تحديد عرض الأعمدة
        self.details_tree.column('sentence', width=400)
        self.details_tree.column('similarity', width=100)
        self.details_tree.column('type', width=120)
        self.details_tree.column('source', width=200)
        
        self.details_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # شريط التمرير
        details_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.details_tree.yview)
        details_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.details_tree.configure(yscrollcommand=details_scrollbar.set)
        
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(0, weight=1)
    
    def create_sources_tab(self):
        """
        إنشاء تبويب المصادر
        Create sources tab
        """
        sources_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(sources_frame, text="المصادر المطابقة")
        
        self.sources_text = scrolledtext.ScrolledText(sources_frame, height=20, state='disabled')
        self.sources_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        sources_frame.columnconfigure(0, weight=1)
        sources_frame.rowconfigure(0, weight=1)
    
    def create_status_bar(self, parent):
        """
        إنشاء شريط الحالة
        Create status bar
        """
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_var = tk.StringVar(value="جاهز")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        self.status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
    
    def init_components(self):
        """
        تهيئة المكونات الأساسية في خيط منفصل
        Initialize core components in separate thread
        """
        def init_worker():
            try:
                self.update_status("جاري تحميل نماذج الذكاء الاصطناعي...")
                self.detector = PlagiarismDetector()
                
                self.update_status("جاري تهيئة مدير المصادر...")
                self.source_manager = SourceManager()
                
                self.update_status("جاهز للاستخدام")
                
            except Exception as e:
                self.logger.error(f"خطأ في تهيئة المكونات: {e}")
                self.update_status(f"خطأ في التهيئة: {str(e)}")
                messagebox.showerror("خطأ", f"فشل في تهيئة التطبيق:\n{str(e)}")
        
        thread = threading.Thread(target=init_worker, daemon=True)
        thread.start()
    
    def update_status(self, message: str):
        """
        تحديث شريط الحالة
        Update status bar
        """
        self.root.after(0, lambda: self.status_var.set(message))
    
    def browse_file(self):
        """
        تصفح واختيار ملف
        Browse and select file
        """
        file_types = [
            ("جميع الملفات المدعومة", "*.pdf;*.docx;*.doc;*.txt"),
            ("ملفات PDF", "*.pdf"),
            ("ملفات Word", "*.docx;*.doc"),
            ("ملفات نصية", "*.txt"),
            ("جميع الملفات", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="اختر ملف للفحص",
            filetypes=file_types
        )
        
        if file_path:
            self.current_file_path = file_path
            self.file_path_var.set(file_path)
            
            # عرض معلومات الملف
            file_info = self.file_processor.get_file_info(file_path)
            info_text = f"الاسم: {file_info['name']} | الحجم: {file_info['size']/1024:.1f} KB | النوع: {file_info['extension']}"
            
            if file_info['is_supported']:
                self.file_info_label.config(text=info_text, foreground='#27ae60')
                self.analyze_button.config(state='normal')
            else:
                self.file_info_label.config(text=f"{info_text} (غير مدعوم)", foreground='#e74c3c')
                self.analyze_button.config(state='disabled')
    
    def start_analysis(self):
        """
        بدء تحليل الملف
        Start file analysis
        """
        if not self.current_file_path or self.is_analyzing:
            return
        
        if not self.detector or not self.source_manager:
            messagebox.showerror("خطأ", "لم يتم تهيئة المكونات بعد. يرجى الانتظار.")
            return
        
        self.is_analyzing = True
        self.analyze_button.config(state='disabled')
        self.progress_bar.grid()
        self.progress_var.set(0)
        
        # تشغيل التحليل في خيط منفصل
        thread = threading.Thread(target=self.analyze_file_worker, daemon=True)
        thread.start()
    
    def analyze_file_worker(self):
        """
        عامل تحليل الملف
        File analysis worker
        """
        try:
            # استخراج النص من الملف
            self.update_status("جاري استخراج النص من الملف...")
            self.root.after(0, lambda: self.progress_var.set(10))
            
            file_result = self.file_processor.extract_text(self.current_file_path)
            
            if not file_result['success']:
                raise Exception(file_result['error_message'])
            
            text = file_result['text']
            
            # جمع المصادر المرجعية
            self.update_status("جاري جمع المصادر المرجعية...")
            self.root.after(0, lambda: self.progress_var.set(30))
            
            sources = self.source_manager.collect_sources_for_text(text)
            
            # تحليل النص
            self.update_status("جاري تحليل النص وفحص الاستلال...")
            self.root.after(0, lambda: self.progress_var.set(60))
            
            self.analysis_results = self.detector.analyze_text_against_sources(text, sources)
            
            # إنتاج التقرير
            self.update_status("جاري إنتاج التقرير...")
            self.root.after(0, lambda: self.progress_var.set(90))
            
            summary = self.detector.generate_summary_report(self.analysis_results)
            
            # تحديث الواجهة
            self.root.after(0, lambda: self.update_results_display(summary))
            self.root.after(0, lambda: self.progress_var.set(100))
            
            self.update_status("تم الانتهاء من التحليل")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الملف: {e}")
            self.update_status(f"خطأ في التحليل: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("خطأ", f"فشل في تحليل الملف:\n{str(e)}"))
        
        finally:
            self.is_analyzing = False
            self.root.after(0, lambda: self.analyze_button.config(state='normal'))
            self.root.after(0, lambda: self.progress_bar.grid_remove())
    
    def update_results_display(self, summary: Dict):
        """
        تحديث عرض النتائج
        Update results display
        """
        # تحديث الملخص
        percentage = summary['overall_plagiarism_percentage']
        self.plagiarism_percentage_var.set(f"{percentage:.1f}%")
        
        # تحديث لون النسبة حسب المستوى
        if percentage >= 30:
            self.percentage_value_label.config(foreground='#e74c3c')  # أحمر
        elif percentage >= 15:
            self.percentage_value_label.config(foreground='#f39c12')  # برتقالي
        else:
            self.percentage_value_label.config(foreground='#27ae60')  # أخضر
        
        # تحديث مستوى المخاطر
        risk_level = summary['risk_level']
        self.risk_level_var.set(risk_level)
        
        if risk_level == 'عالي':
            self.risk_value_label.config(foreground='#e74c3c')
        elif risk_level == 'متوسط':
            self.risk_value_label.config(foreground='#f39c12')
        else:
            self.risk_value_label.config(foreground='#27ae60')
        
        # تحديث الإحصائيات
        self.update_statistics_display(summary)
        
        # تحديث تفاصيل الجمل المشبوهة
        self.update_details_display()
        
        # تحديث المصادر
        self.update_sources_display()
        
        # تفعيل زر حفظ التقرير
        self.save_report_button.config(state='normal')
    
    def update_statistics_display(self, summary: Dict):
        """
        تحديث عرض الإحصائيات
        Update statistics display
        """
        self.stats_text.config(state='normal')
        self.stats_text.delete(1.0, tk.END)
        
        stats_text = f"""إحصائيات التحليل:

• إجمالي الجمل المشبوهة: {summary['total_suspicious_sentences']}
• عدد المصادر المطابقة: {summary['sources_count']}
• مستوى المخاطر: {summary['risk_level']}

أنواع الاستلال المكتشفة:
"""
        
        for plag_type, count in summary['plagiarism_types'].items():
            stats_text += f"• {plag_type}: {count} جملة\n"
        
        self.stats_text.insert(1.0, stats_text)
        self.stats_text.config(state='disabled')
    
    def update_details_display(self):
        """
        تحديث عرض تفاصيل الجمل المشبوهة
        Update suspicious sentences details display
        """
        # مسح البيانات السابقة
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        # إضافة الجمل المشبوهة
        for sentence_data in self.analysis_results['suspicious_sentences']:
            sentence = sentence_data['sentence'][:100] + "..." if len(sentence_data['sentence']) > 100 else sentence_data['sentence']
            similarity = f"{sentence_data['max_similarity']:.2f}"
            plag_type = sentence_data['plagiarism_type']
            
            # أخذ أول مصدر مطابق
            source = "غير محدد"
            if sentence_data['matches']:
                source = sentence_data['matches'][0]['source_title'][:50]
            
            self.details_tree.insert('', 'end', values=(sentence, similarity, plag_type, source))
    
    def update_sources_display(self):
        """
        تحديث عرض المصادر
        Update sources display
        """
        self.sources_text.config(state='normal')
        self.sources_text.delete(1.0, tk.END)
        
        sources_text = "المصادر المطابقة:\n\n"
        
        for source_title in self.analysis_results['sources_matched']:
            sources_text += f"• {source_title}\n"
        
        self.sources_text.insert(1.0, sources_text)
        self.sources_text.config(state='disabled')
    
    def save_report(self):
        """
        حفظ التقرير كملف PDF
        Save report as PDF file
        """
        if not self.analysis_results:
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ التقرير",
            defaultextension=".pdf",
            filetypes=[("ملفات PDF", "*.pdf"), ("جميع الملفات", "*.*")]
        )

        if file_path:
            try:
                # إنتاج التقرير
                summary = self.detector.generate_summary_report(self.analysis_results)
                success = self.report_generator.generate_report(
                    self.analysis_results,
                    summary,
                    self.current_file_path,
                    file_path
                )

                if success:
                    messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{file_path}")
                else:
                    messagebox.showerror("خطأ", "فشل في إنتاج التقرير")

            except Exception as e:
                self.logger.error(f"خطأ في حفظ التقرير: {e}")
                messagebox.showerror("خطأ", f"فشل في حفظ التقرير:\n{str(e)}")
    
    def run(self):
        """
        تشغيل التطبيق
        Run the application
        """
        self.root.mainloop()

if __name__ == "__main__":
    app = PlagiarismDetectorGUI()
    app.run()
