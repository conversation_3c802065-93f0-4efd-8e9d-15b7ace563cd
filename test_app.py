"""
اختبار تطبيق فحص الاستلال
Test Plagiarism Detection Application
"""

import sys
import os
import tempfile
import logging
from pathlib import Path

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent))

from src.core.plagiarism_detector import PlagiarismDetector
from src.core.source_manager import SourceManager
from src.utils.file_processor import FileProcessor
from src.utils.report_generator import ReportGenerator

def setup_test_logging():
    """
    إعداد نظام السجلات للاختبار
    Setup logging for testing
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def create_test_text_file():
    """
    إنشاء ملف نصي للاختبار
    Create test text file
    """
    test_content = """
    الذكاء الاصطناعي هو محاكاة الذكاء البشري في الآلات المبرمجة للتفكير والتعلم مثل البشر.
    يمكن تطبيق هذا المصطلح على أي آلة تظهر سمات مرتبطة بالعقل البشري مثل التعلم وحل المشكلات.
    
    Artificial intelligence is the simulation of human intelligence in machines that are programmed to think and learn like humans.
    This term can be applied to any machine that exhibits traits associated with the human mind such as learning and problem-solving.
    
    تطبيقات الذكاء الاصطناعي تشمل معالجة اللغات الطبيعية، والتعرف على الكلام، والرؤية الحاسوبية.
    هذه التقنيات تستخدم في مختلف المجالات مثل الطب والتعليم والنقل والأمن.
    """
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        return f.name

def test_file_processor():
    """
    اختبار معالج الملفات
    Test file processor
    """
    print("اختبار معالج الملفات...")
    
    processor = FileProcessor()
    
    # إنشاء ملف اختبار
    test_file = create_test_text_file()
    
    try:
        # اختبار استخراج النص
        result = processor.extract_text(test_file)
        
        if result['success']:
            print(f"✓ تم استخراج النص بنجاح ({len(result['text'])} حرف)")
            return result['text']
        else:
            print(f"✗ فشل في استخراج النص: {result['error_message']}")
            return None
            
    finally:
        # حذف الملف المؤقت
        os.unlink(test_file)

def test_source_manager():
    """
    اختبار مدير المصادر
    Test source manager
    """
    print("اختبار مدير المصادر...")
    
    try:
        manager = SourceManager()
        
        # اختبار إضافة مصدر
        success = manager.add_source(
            title="مصدر اختبار",
            content="هذا نص تجريبي لاختبار إضافة المصادر إلى قاعدة البيانات. يحتوي على معلومات حول الذكاء الاصطناعي والتعلم الآلي.",
            url="http://test.com",
            source_type="test",
            language="ar"
        )
        
        if success:
            print("✓ تم إضافة المصدر بنجاح")
        else:
            print("✓ المصدر موجود مسبقاً أو فشل في الإضافة")
        
        # اختبار الحصول على إحصائيات
        stats = manager.get_database_stats()
        print(f"✓ إحصائيات قاعدة البيانات: {stats['total_sources']} مصدر")
        
        return manager
        
    except Exception as e:
        print(f"✗ خطأ في اختبار مدير المصادر: {e}")
        return None

def test_plagiarism_detector(text, source_manager):
    """
    اختبار محرك فحص الاستلال
    Test plagiarism detector
    """
    print("اختبار محرك فحص الاستلال...")
    
    try:
        detector = PlagiarismDetector()
        
        # جمع مصادر للاختبار
        sources = source_manager.collect_sources_for_text(text)
        print(f"✓ تم جمع {len(sources)} مصدر للمقارنة")
        
        # تحليل النص
        results = detector.analyze_text_against_sources(text, sources)
        
        print(f"✓ نسبة الاستلال: {results['total_plagiarism_percentage']:.1f}%")
        print(f"✓ عدد الجمل المشبوهة: {len(results['suspicious_sentences'])}")
        print(f"✓ عدد المصادر المطابقة: {len(results['sources_matched'])}")
        
        # إنتاج ملخص
        summary = detector.generate_summary_report(results)
        print(f"✓ مستوى المخاطر: {summary['risk_level']}")
        
        return detector, results, summary
        
    except Exception as e:
        print(f"✗ خطأ في اختبار محرك فحص الاستلال: {e}")
        return None, None, None

def test_report_generator(results, summary, test_file_path):
    """
    اختبار مولد التقارير
    Test report generator
    """
    print("اختبار مولد التقارير...")
    
    try:
        generator = ReportGenerator()
        
        # إنشاء تقرير مؤقت
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
            report_path = f.name
        
        # إنتاج التقرير
        success = generator.generate_report(results, summary, test_file_path, report_path)
        
        if success and os.path.exists(report_path):
            file_size = os.path.getsize(report_path)
            print(f"✓ تم إنتاج التقرير بنجاح ({file_size} بايت)")
            
            # حذف التقرير المؤقت
            os.unlink(report_path)
            return True
        else:
            print("✗ فشل في إنتاج التقرير")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في اختبار مولد التقارير: {e}")
        return False

def test_performance():
    """
    اختبار الأداء
    Test performance
    """
    print("اختبار الأداء...")
    
    import time
    
    # نص طويل للاختبار
    long_text = """
    الذكاء الاصطناعي هو مجال واسع في علوم الحاسوب يهدف إلى إنشاء أنظمة قادرة على أداء مهام تتطلب ذكاءً بشرياً.
    يشمل هذا المجال العديد من التقنيات مثل التعلم الآلي، والشبكات العصبية، ومعالجة اللغات الطبيعية.
    
    التعلم الآلي هو فرع من الذكاء الاصطناعي يركز على تطوير خوارزميات تمكن الحاسوب من التعلم من البيانات.
    هذه الخوارزميات تستطيع تحسين أدائها تلقائياً من خلال الخبرة دون الحاجة لبرمجة صريحة.
    
    الشبكات العصبية الاصطناعية مستوحاة من طريقة عمل الدماغ البشري وتتكون من عقد مترابطة تعالج المعلومات.
    هذه الشبكات فعالة جداً في مهام مثل التعرف على الصور والكلام والترجمة الآلية.
    """ * 3  # تكرار النص لجعله أطول
    
    try:
        start_time = time.time()
        
        # اختبار سرعة المعالجة
        detector = PlagiarismDetector()
        sentences = detector.extract_sentences(long_text)
        
        processing_time = time.time() - start_time
        
        print(f"✓ تم معالجة {len(sentences)} جملة في {processing_time:.2f} ثانية")
        print(f"✓ متوسط الوقت لكل جملة: {processing_time/len(sentences):.3f} ثانية")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار الأداء: {e}")
        return False

def main():
    """
    الدالة الرئيسية للاختبار
    Main testing function
    """
    print("=" * 60)
    print("اختبار تطبيق فحص الاستلال بالذكاء الاصطناعي")
    print("Testing AI Plagiarism Detection Application")
    print("=" * 60)
    
    setup_test_logging()
    
    # اختبار معالج الملفات
    text = test_file_processor()
    if not text:
        print("فشل في اختبار معالج الملفات")
        return
    
    print()
    
    # اختبار مدير المصادر
    source_manager = test_source_manager()
    if not source_manager:
        print("فشل في اختبار مدير المصادر")
        return
    
    print()
    
    # اختبار محرك فحص الاستلال
    detector, results, summary = test_plagiarism_detector(text, source_manager)
    if not detector:
        print("فشل في اختبار محرك فحص الاستلال")
        return
    
    print()
    
    # اختبار مولد التقارير
    test_file_path = "test_document.txt"
    if not test_report_generator(results, summary, test_file_path):
        print("فشل في اختبار مولد التقارير")
        return
    
    print()
    
    # اختبار الأداء
    if not test_performance():
        print("فشل في اختبار الأداء")
        return
    
    print()
    print("=" * 60)
    print("✓ تم اجتياز جميع الاختبارات بنجاح!")
    print("التطبيق جاهز للاستخدام")
    print("=" * 60)

if __name__ == "__main__":
    main()
