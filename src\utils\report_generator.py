"""
مولد التقارير - إنشاء تقارير PDF مفصلة لنتائج فحص الاستلال
Report Generator - Creating detailed PDF reports for plagiarism detection results
"""

from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # استخدام backend غير تفاعلي
import io
from reportlab.platypus import Image
import os
from datetime import datetime
from typing import Dict, List
import logging
import arabic_reshaper
from bidi.algorithm import get_display

class ReportGenerator:
    """
    فئة مولد التقارير
    Report generator class
    """
    
    def __init__(self):
        """
        تهيئة مولد التقارير
        Initialize report generator
        """
        self.logger = logging.getLogger(__name__)
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """
        إعداد الخطوط للدعم العربي
        Setup fonts for Arabic support
        """
        try:
            # محاولة تحميل خط عربي (يمكن تحميل خط مناسب)
            # font_path = "assets/fonts/NotoSansArabic-Regular.ttf"
            # if os.path.exists(font_path):
            #     pdfmetrics.registerFont(TTFont('Arabic', font_path))
            #     self.arabic_font = 'Arabic'
            # else:
            self.arabic_font = 'Helvetica'  # استخدام خط افتراضي
        except Exception as e:
            self.logger.warning(f"فشل في تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
    
    def setup_styles(self):
        """
        إعداد أنماط النص
        Setup text styles
        """
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.styles.add(ParagraphStyle(
            name='ArabicTitle',
            parent=self.styles['Title'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.darkblue
        ))
        
        # نمط العنوان الفرعي
        self.styles.add(ParagraphStyle(
            name='ArabicHeading',
            parent=self.styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=12,
            textColor=colors.darkgreen
        ))
        
        # نمط النص العادي
        self.styles.add(ParagraphStyle(
            name='ArabicNormal',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=11,
            alignment=TA_RIGHT,
            spaceAfter=6
        ))
        
        # نمط النص المحاذي لليسار (للأرقام والنسب)
        self.styles.add(ParagraphStyle(
            name='LeftAlign',
            parent=self.styles['Normal'],
            fontName='Helvetica',
            fontSize=11,
            alignment=TA_LEFT,
            spaceAfter=6
        ))
    
    def format_arabic_text(self, text: str) -> str:
        """
        تنسيق النص العربي للعرض الصحيح
        Format Arabic text for proper display
        """
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text
    
    def create_chart(self, data: Dict) -> io.BytesIO:
        """
        إنشاء رسم بياني للنتائج
        Create chart for results
        """
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # رسم بياني دائري لأنواع الاستلال
            if data.get('plagiarism_types'):
                types = list(data['plagiarism_types'].keys())
                counts = list(data['plagiarism_types'].values())
                
                ax1.pie(counts, labels=types, autopct='%1.1f%%', startangle=90)
                ax1.set_title('Distribution of Plagiarism Types')
            
            # رسم بياني شريطي للنسب
            categories = ['Original', 'Plagiarized']
            percentages = [
                100 - data.get('overall_plagiarism_percentage', 0),
                data.get('overall_plagiarism_percentage', 0)
            ]
            colors_list = ['green', 'red']
            
            ax2.bar(categories, percentages, color=colors_list)
            ax2.set_ylabel('Percentage (%)')
            ax2.set_title('Overall Plagiarism Analysis')
            ax2.set_ylim(0, 100)
            
            # حفظ الرسم في buffer
            img_buffer = io.BytesIO()
            plt.tight_layout()
            plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
            img_buffer.seek(0)
            plt.close()
            
            return img_buffer
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الرسم البياني: {e}")
            return None
    
    def generate_report(self, analysis_results: Dict, summary: Dict, 
                       file_path: str, output_path: str) -> bool:
        """
        إنتاج التقرير الكامل
        Generate complete report
        """
        try:
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # قائمة عناصر التقرير
            story = []
            
            # إضافة العنوان الرئيسي
            title_text = self.format_arabic_text("تقرير فحص الاستلال بالذكاء الاصطناعي")
            story.append(Paragraph(title_text, self.styles['ArabicTitle']))
            story.append(Spacer(1, 20))
            
            # معلومات الملف
            file_info_text = self.format_arabic_text(f"الملف المفحوص: {os.path.basename(file_path)}")
            story.append(Paragraph(file_info_text, self.styles['ArabicNormal']))
            
            date_text = f"تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            story.append(Paragraph(date_text, self.styles['LeftAlign']))
            story.append(Spacer(1, 20))
            
            # الملخص التنفيذي
            summary_title = self.format_arabic_text("الملخص التنفيذي")
            story.append(Paragraph(summary_title, self.styles['ArabicHeading']))
            
            # نسبة الاستلال الإجمالية
            percentage = summary.get('overall_plagiarism_percentage', 0)
            percentage_text = f"نسبة الاستلال الإجمالية: {percentage:.1f}%"
            story.append(Paragraph(self.format_arabic_text(percentage_text), self.styles['ArabicNormal']))
            
            # مستوى المخاطر
            risk_level = summary.get('risk_level', 'غير محدد')
            risk_text = f"مستوى المخاطر: {risk_level}"
            story.append(Paragraph(self.format_arabic_text(risk_text), self.styles['ArabicNormal']))
            
            # عدد الجمل المشبوهة
            suspicious_count = summary.get('total_suspicious_sentences', 0)
            suspicious_text = f"عدد الجمل المشبوهة: {suspicious_count}"
            story.append(Paragraph(self.format_arabic_text(suspicious_text), self.styles['ArabicNormal']))
            
            # عدد المصادر المطابقة
            sources_count = summary.get('sources_count', 0)
            sources_text = f"عدد المصادر المطابقة: {sources_count}"
            story.append(Paragraph(self.format_arabic_text(sources_text), self.styles['ArabicNormal']))
            
            story.append(Spacer(1, 20))
            
            # إضافة الرسم البياني
            chart_buffer = self.create_chart(summary)
            if chart_buffer:
                chart_img = Image(chart_buffer, width=6*inch, height=2.5*inch)
                story.append(chart_img)
                story.append(Spacer(1, 20))
            
            # أنواع الاستلال
            if summary.get('plagiarism_types'):
                types_title = self.format_arabic_text("أنواع الاستلال المكتشفة")
                story.append(Paragraph(types_title, self.styles['ArabicHeading']))
                
                for plag_type, count in summary['plagiarism_types'].items():
                    type_text = f"• {plag_type}: {count} جملة"
                    story.append(Paragraph(self.format_arabic_text(type_text), self.styles['ArabicNormal']))
                
                story.append(Spacer(1, 20))
            
            # الجمل المشبوهة التفصيلية
            if analysis_results.get('suspicious_sentences'):
                details_title = self.format_arabic_text("تفاصيل الجمل المشبوهة")
                story.append(Paragraph(details_title, self.styles['ArabicHeading']))
                
                # إنشاء جدول للجمل المشبوهة
                table_data = [['الجملة', 'نسبة التشابه', 'نوع الاستلال', 'المصدر']]
                
                for i, sentence_data in enumerate(analysis_results['suspicious_sentences'][:20]):  # أول 20 جملة
                    sentence = sentence_data['sentence'][:100] + "..." if len(sentence_data['sentence']) > 100 else sentence_data['sentence']
                    similarity = f"{sentence_data['max_similarity']:.2f}"
                    plag_type = sentence_data['plagiarism_type']
                    
                    source = "غير محدد"
                    if sentence_data['matches']:
                        source = sentence_data['matches'][0]['source_title'][:50]
                    
                    table_data.append([
                        self.format_arabic_text(sentence),
                        similarity,
                        self.format_arabic_text(plag_type),
                        self.format_arabic_text(source)
                    ])
                
                # إنشاء الجدول
                table = Table(table_data, colWidths=[3*inch, 1*inch, 1.5*inch, 2*inch])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('FONTSIZE', (0, 1), (-1, -1), 9),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ]))
                
                story.append(table)
                story.append(Spacer(1, 20))
            
            # المصادر المطابقة
            if analysis_results.get('sources_matched'):
                sources_title = self.format_arabic_text("المصادر المطابقة")
                story.append(Paragraph(sources_title, self.styles['ArabicHeading']))
                
                for source in analysis_results['sources_matched']:
                    source_text = f"• {source}"
                    story.append(Paragraph(self.format_arabic_text(source_text), self.styles['ArabicNormal']))
                
                story.append(Spacer(1, 20))
            
            # التوصيات
            recommendations_title = self.format_arabic_text("التوصيات")
            story.append(Paragraph(recommendations_title, self.styles['ArabicHeading']))
            
            if percentage >= 30:
                rec_text = "نسبة الاستلال عالية جداً. يُنصح بمراجعة شاملة للنص وإعادة كتابة الأجزاء المستلة."
            elif percentage >= 15:
                rec_text = "نسبة الاستلال متوسطة. يُنصح بمراجعة الجمل المشبوهة وإضافة المراجع المناسبة."
            else:
                rec_text = "نسبة الاستلال منخفضة. النص مقبول مع ضرورة التأكد من صحة المراجع."
            
            story.append(Paragraph(self.format_arabic_text(rec_text), self.styles['ArabicNormal']))
            
            # إضافة تذييل
            story.append(Spacer(1, 30))
            footer_text = self.format_arabic_text("تم إنتاج هذا التقرير بواسطة فاحص الاستلال بالذكاء الاصطناعي")
            story.append(Paragraph(footer_text, self.styles['ArabicNormal']))
            
            # بناء المستند
            doc.build(story)
            
            self.logger.info(f"تم إنتاج التقرير بنجاح: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنتاج التقرير: {e}")
            return False
    
    def generate_simple_report(self, analysis_results: Dict, summary: Dict, 
                              file_path: str, output_path: str) -> bool:
        """
        إنتاج تقرير مبسط
        Generate simple report
        """
        try:
            with open(output_path.replace('.pdf', '.txt'), 'w', encoding='utf-8') as f:
                f.write("تقرير فحص الاستلال\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"الملف المفحوص: {os.path.basename(file_path)}\n")
                f.write(f"تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("الملخص:\n")
                f.write(f"نسبة الاستلال: {summary.get('overall_plagiarism_percentage', 0):.1f}%\n")
                f.write(f"مستوى المخاطر: {summary.get('risk_level', 'غير محدد')}\n")
                f.write(f"عدد الجمل المشبوهة: {summary.get('total_suspicious_sentences', 0)}\n")
                f.write(f"عدد المصادر المطابقة: {summary.get('sources_count', 0)}\n\n")
                
                if summary.get('plagiarism_types'):
                    f.write("أنواع الاستلال:\n")
                    for plag_type, count in summary['plagiarism_types'].items():
                        f.write(f"- {plag_type}: {count} جملة\n")
                    f.write("\n")
                
                if analysis_results.get('suspicious_sentences'):
                    f.write("الجمل المشبوهة:\n")
                    for i, sentence_data in enumerate(analysis_results['suspicious_sentences'][:10]):
                        f.write(f"{i+1}. {sentence_data['sentence'][:200]}...\n")
                        f.write(f"   نسبة التشابه: {sentence_data['max_similarity']:.2f}\n")
                        f.write(f"   نوع الاستلال: {sentence_data['plagiarism_type']}\n\n")
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنتاج التقرير المبسط: {e}")
            return False
