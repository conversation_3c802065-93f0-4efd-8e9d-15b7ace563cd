# دليل البدء السريع - فاحص الاستلال بالذكاء الاصطناعي
# Quick Start Guide - AI Plagiarism Detector

## 🚀 التشغيل السريع (5 دقائق)

### الطريقة الأولى: تشغيل مباشر من الكود

1. **تحميل المتطلبات**:
```bash
pip install -r requirements.txt
```

2. **تشغيل التطبيق**:
```bash
python main.py
```

### الطريقة الثانية: بناء ملف تنفيذي

1. **بناء EXE**:
```bash
python build_exe.py
```

2. **تشغيل التطبيق**:
```bash
dist/PlagiarismDetector.exe
```

## 📋 خطوات الاستخدام

### 1. تشغيل التطبيق
- شغل الملف الرئيسي أو EXE
- انتظر تحميل النماذج (30-60 ثانية في المرة الأولى)

### 2. اختيار الملف
- اضغط زر "تصفح"
- اختر ملف PDF، Word، أو TXT
- تأكد من ظهور معلومات الملف باللون الأخضر

### 3. بدء الفحص
- اضغط "فحص الاستلال"
- انتظر انتهاء التحليل (1-5 دقائق حسب حجم الملف)

### 4. مراجعة النتائج
- راجع نسبة الاستلال في تبويب "ملخص النتائج"
- اطلع على الجمل المشبوهة في تبويب "الجمل المشبوهة"
- راجع المصادر في تبويب "المصادر المطابقة"

### 5. حفظ التقرير
- اضغط "حفظ التقرير (PDF)"
- اختر مكان الحفظ
- سيتم إنتاج تقرير شامل

## ⚡ نصائح للاستخدام الأمثل

### لأفضل النتائج:
- استخدم ملفات تحتوي على أكثر من 100 كلمة
- تأكد من وضوح النص في ملفات PDF
- تجنب الملفات المحمية بكلمة مرور

### لتسريع المعالجة:
- أغلق البرامج الأخرى أثناء التحليل
- استخدم ملفات أصغر من 10 MB
- تأكد من اتصال إنترنت مستقر

## 🔧 حل المشاكل الشائعة

### مشكلة: "فشل في تهيئة التطبيق"
**الحل**: تأكد من تثبيت جميع المتطلبات:
```bash
pip install -r requirements.txt
python -m spacy download en_core_web_sm
```

### مشكلة: "لم يتم العثور على نص في الملف"
**الحل**: 
- تأكد من أن ملف PDF يحتوي على نص قابل للتحديد
- جرب تحويل الملف إلى تنسيق آخر
- تأكد من أن الملف غير محمي

### مشكلة: "التحليل بطيء جداً"
**الحل**:
- قسم الملف الكبير إلى أجزاء أصغر
- أغلق البرامج الأخرى
- تأكد من توفر ذاكرة كافية (4 GB+)

### مشكلة: "خطأ في إنتاج التقرير"
**الحل**:
- تأكد من وجود مساحة كافية على القرص
- تأكد من صلاحيات الكتابة في مجلد الحفظ
- جرب حفظ التقرير في مكان آخر

## 📊 فهم النتائج

### نسب الاستلال:
- **0-15%**: مقبول (مستوى منخفض)
- **15-30%**: يحتاج مراجعة (مستوى متوسط)
- **30%+**: غير مقبول (مستوى عالي)

### أنواع الاستلال:
- **استلال حرفي**: نسخ مباشر للنص
- **إعادة صياغة**: تغيير في الكلمات مع الحفاظ على المعنى
- **استلال معنوي**: تشابه في الأفكار والمفاهيم

### مستويات المخاطر:
- **منخفض**: النص أصلي إلى حد كبير
- **متوسط**: يحتاج مراجعة وإضافة مراجع
- **عالي**: يحتاج إعادة كتابة شاملة

## 🎯 أمثلة عملية

### مثال 1: فحص بحث أكاديمي
1. افتح ملف البحث (PDF)
2. شغل الفحص
3. راجع الجمل المشبوهة
4. أضف المراجع المناسبة
5. احفظ التقرير للمراجعة

### مثال 2: فحص مقال صحفي
1. انسخ المقال في ملف TXT
2. شغل الفحص
3. تحقق من المصادر المطابقة
4. تأكد من الاقتباس الصحيح

## 📞 الحصول على المساعدة

### للمشاكل التقنية:
- راجع ملف README.md للتفاصيل الكاملة
- شغل test_app.py للتأكد من سلامة التثبيت
- تحقق من ملف السجلات (logs/app.log)

### للاستفسارات:
- راجع الوثائق في المشروع
- ابحث في المشاكل الشائعة أعلاه
- تواصل مع فريق الدعم

## 🔄 التحديثات

للحصول على أحدث إصدار:
1. حمل الإصدار الجديد
2. احتفظ بنسخة احتياطية من قاعدة البيانات
3. ثبت الإصدار الجديد
4. شغل الاختبارات للتأكد من سلامة التحديث

---

**نصيحة**: احتفظ بهذا الدليل مرجعاً سريعاً أثناء استخدام التطبيق!
