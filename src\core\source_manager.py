"""
مدير المصادر - جمع وإدارة المصادر المرجعية للمقارنة
Source Manager - Collecting and managing reference sources for comparison
"""

import sqlite3
import requests
import wikipedia
from bs4 import BeautifulSoup
import logging
import time
import hashlib
from typing import List, Dict, Optional
import os
import json
from urllib.parse import urljoin, urlparse
import re

class SourceManager:
    """
    فئة إدارة المصادر المرجعية
    Reference sources management class
    """
    
    def __init__(self, db_path: str = "data/sources/sources.db"):
        """
        تهيئة مدير المصادر
        Initialize source manager
        """
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # تهيئة قاعدة البيانات
        self.init_database()
        
        # تحديد ويكيبيديا للغة العربية والإنجليزية
        self.wikipedia_languages = ['ar', 'en']
    
    def init_database(self):
        """
        تهيئة قاعدة البيانات
        Initialize database
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول المصادر الرئيسي
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sources (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        content TEXT NOT NULL,
                        url TEXT,
                        source_type TEXT NOT NULL,
                        language TEXT,
                        content_hash TEXT UNIQUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # فهرس للبحث السريع
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_content_hash ON sources(content_hash)
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_source_type ON sources(source_type)
                ''')
                
                conn.commit()
                self.logger.info("تم تهيئة قاعدة البيانات بنجاح")
                
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            raise
    
    def calculate_content_hash(self, content: str) -> str:
        """
        حساب hash للمحتوى لتجنب التكرار
        Calculate content hash to avoid duplication
        """
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def add_source(self, title: str, content: str, url: str = "", 
                   source_type: str = "web", language: str = "unknown") -> bool:
        """
        إضافة مصدر جديد إلى قاعدة البيانات
        Add new source to database
        """
        try:
            # تنظيف المحتوى
            content = content.strip()
            if len(content) < 100:  # تجاهل المحتوى القصير جداً
                return False
            
            content_hash = self.calculate_content_hash(content)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # فحص وجود المحتوى مسبقاً
                cursor.execute('SELECT id FROM sources WHERE content_hash = ?', (content_hash,))
                if cursor.fetchone():
                    return False  # المحتوى موجود مسبقاً
                
                # إضافة المصدر الجديد
                cursor.execute('''
                    INSERT INTO sources (title, content, url, source_type, language, content_hash)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (title, content, url, source_type, language, content_hash))
                
                conn.commit()
                self.logger.info(f"تم إضافة مصدر جديد: {title}")
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المصدر: {e}")
            return False
    
    def search_wikipedia(self, query: str, max_results: int = 5) -> List[Dict]:
        """
        البحث في ويكيبيديا
        Search Wikipedia
        """
        sources = []
        
        for lang in self.wikipedia_languages:
            try:
                wikipedia.set_lang(lang)
                
                # البحث عن المقالات
                search_results = wikipedia.search(query, results=max_results)
                
                for title in search_results[:max_results]:
                    try:
                        # الحصول على المقال
                        page = wikipedia.page(title)
                        
                        # إضافة المصدر
                        source_data = {
                            'title': f"ويكيبيديا ({lang}): {page.title}",
                            'content': page.content,
                            'url': page.url,
                            'source_type': 'wikipedia',
                            'language': lang
                        }
                        
                        sources.append(source_data)
                        
                        # إضافة إلى قاعدة البيانات
                        self.add_source(**source_data)
                        
                        # تأخير قصير لتجنب الحظر
                        time.sleep(0.5)
                        
                    except wikipedia.exceptions.DisambiguationError as e:
                        # في حالة وجود عدة معاني، أخذ الأول
                        try:
                            page = wikipedia.page(e.options[0])
                            source_data = {
                                'title': f"ويكيبيديا ({lang}): {page.title}",
                                'content': page.content,
                                'url': page.url,
                                'source_type': 'wikipedia',
                                'language': lang
                            }
                            sources.append(source_data)
                            self.add_source(**source_data)
                        except:
                            continue
                            
                    except wikipedia.exceptions.PageError:
                        continue
                    except Exception as e:
                        self.logger.warning(f"خطأ في جلب مقال ويكيبيديا {title}: {e}")
                        continue
                        
            except Exception as e:
                self.logger.error(f"خطأ في البحث في ويكيبيديا ({lang}): {e}")
                continue
        
        return sources
    
    def scrape_academic_sources(self, query: str, max_results: int = 3) -> List[Dict]:
        """
        جمع مصادر أكاديمية من مواقع مختلفة
        Scrape academic sources from various websites
        """
        sources = []
        
        # مواقع أكاديمية للبحث فيها
        academic_sites = [
            f"https://scholar.google.com/scholar?q={query}",
            f"https://www.researchgate.net/search?q={query}",
        ]
        
        for site_url in academic_sites:
            try:
                response = self.session.get(site_url, timeout=10)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # استخراج النصوص من الصفحة
                    text_content = soup.get_text()
                    
                    # تنظيف النص
                    cleaned_text = re.sub(r'\s+', ' ', text_content).strip()
                    
                    if len(cleaned_text) > 500:
                        source_data = {
                            'title': f"مصدر أكاديمي: {urlparse(site_url).netloc}",
                            'content': cleaned_text[:5000],  # أخذ أول 5000 حرف
                            'url': site_url,
                            'source_type': 'academic',
                            'language': 'mixed'
                        }
                        
                        sources.append(source_data)
                        self.add_source(**source_data)
                
                time.sleep(1)  # تأخير لتجنب الحظر
                
            except Exception as e:
                self.logger.warning(f"خطأ في جمع المصادر من {site_url}: {e}")
                continue
        
        return sources
    
    def get_sources_by_keywords(self, keywords: List[str], limit: int = 50) -> List[Dict]:
        """
        الحصول على المصادر من قاعدة البيانات بناءً على الكلمات المفتاحية
        Get sources from database based on keywords
        """
        sources = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # بناء استعلام البحث
                search_conditions = []
                params = []
                
                for keyword in keywords:
                    search_conditions.append("(title LIKE ? OR content LIKE ?)")
                    params.extend([f"%{keyword}%", f"%{keyword}%"])
                
                if search_conditions:
                    query = f'''
                        SELECT title, content, url, source_type, language
                        FROM sources
                        WHERE {" OR ".join(search_conditions)}
                        ORDER BY last_updated DESC
                        LIMIT ?
                    '''
                    params.append(limit)
                    
                    cursor.execute(query, params)
                    results = cursor.fetchall()
                    
                    for row in results:
                        sources.append({
                            'title': row[0],
                            'content': row[1],
                            'url': row[2],
                            'source_type': row[3],
                            'language': row[4]
                        })
                
        except Exception as e:
            self.logger.error(f"خطأ في البحث في قاعدة البيانات: {e}")
        
        return sources
    
    def extract_keywords_from_text(self, text: str) -> List[str]:
        """
        استخراج الكلمات المفتاحية من النص
        Extract keywords from text
        """
        # إزالة الكلمات الشائعة والرموز
        stop_words = {
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك',
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with'
        }
        
        # تنظيف النص واستخراج الكلمات
        words = re.findall(r'\b\w{3,}\b', text.lower())
        keywords = [word for word in words if word not in stop_words]
        
        # أخذ أهم الكلمات (الأكثر تكراراً)
        from collections import Counter
        word_counts = Counter(keywords)
        top_keywords = [word for word, count in word_counts.most_common(10)]
        
        return top_keywords
    
    def collect_sources_for_text(self, text: str) -> List[Dict]:
        """
        جمع المصادر المناسبة لنص معين
        Collect appropriate sources for a given text
        """
        all_sources = []
        
        # استخراج الكلمات المفتاحية
        keywords = self.extract_keywords_from_text(text)
        
        if not keywords:
            return all_sources
        
        # البحث في قاعدة البيانات المحلية أولاً
        local_sources = self.get_sources_by_keywords(keywords, limit=20)
        all_sources.extend(local_sources)
        
        # إذا لم نجد مصادر كافية، البحث في ويكيبيديا
        if len(all_sources) < 10:
            for keyword in keywords[:3]:  # أخذ أهم 3 كلمات
                wiki_sources = self.search_wikipedia(keyword, max_results=3)
                all_sources.extend(wiki_sources)
        
        # إزالة المصادر المكررة
        unique_sources = []
        seen_hashes = set()
        
        for source in all_sources:
            content_hash = self.calculate_content_hash(source['content'])
            if content_hash not in seen_hashes:
                unique_sources.append(source)
                seen_hashes.add(content_hash)
        
        return unique_sources[:30]  # إرجاع أفضل 30 مصدر
    
    def get_database_stats(self) -> Dict:
        """
        الحصول على إحصائيات قاعدة البيانات
        Get database statistics
        """
        stats = {
            'total_sources': 0,
            'by_type': {},
            'by_language': {}
        }
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # إجمالي المصادر
                cursor.execute('SELECT COUNT(*) FROM sources')
                stats['total_sources'] = cursor.fetchone()[0]
                
                # حسب النوع
                cursor.execute('SELECT source_type, COUNT(*) FROM sources GROUP BY source_type')
                for row in cursor.fetchall():
                    stats['by_type'][row[0]] = row[1]
                
                # حسب اللغة
                cursor.execute('SELECT language, COUNT(*) FROM sources GROUP BY language')
                for row in cursor.fetchall():
                    stats['by_language'][row[0]] = row[1]
                    
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات قاعدة البيانات: {e}")
        
        return stats
